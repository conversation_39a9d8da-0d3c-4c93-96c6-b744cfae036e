<template>
  <a-modal v-model:visible="visible" title="导入进度" :width="600" :closable="false" :maskClosable="false" :footer="null" :destroyOnClose="true">
    <div class="import-progress-container">
      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-header">
          <h4>处理进度</h4>
          <span class="progress-text">{{ displayProgress }}%</span>
        </div>
        <a-progress :percent="displayProgress" :status="getProgressStatus()" :stroke-color="getProgressColor()" :show-info="false" />
        <div class="progress-details">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总记录数" :value="displayTotalCount" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="已处理" :value="displayProcessedCount" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="成功" :value="displaySuccessCount" value-style="color: #52c41a" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="失败" :value="displayFailureCount" value-style="color: #ff4d4f" />
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 当前状态消息 -->
      <div class="status-message" v-if="progressInfo?.message">
        <a-alert :message="progressInfo.message" :type="getAlertType()" show-icon />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button v-if="canClose" type="primary" @click="handleClose">关闭</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, onUnmounted, ref, watch } from 'vue';
  import { message, Statistic } from 'ant-design-vue';
  import { getImportProgress } from '/@/api/asyncImport';

  // 注册组件
  const AStatistic = Statistic;

  interface ProgressInfo {
    taskId: string;
    eventType: string;
    progress: number;
    totalCount: number;
    processedCount: number;
    successCount: number;
    failureCount: number;
    message: string;
    errorMessage?: string;
    timestamp: number;
  }

  const props = defineProps<{
    taskId: string;
  }>();

  const emit = defineEmits<{
    close: [];
  }>();

  // 响应式数据
  const visible = ref(true);
  const progressInfo = ref<ProgressInfo | null>(null);
  let pollInterval: NodeJS.Timeout | null = null;

  // 计算属性
  const displayProgress = computed(() => {
    if (!progressInfo.value) return 0;
    const progress = Number(progressInfo.value.progress);
    return isNaN(progress) ? 0 : Math.min(100, Math.max(0, Math.round(progress)));
  });

  const displayTotalCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.totalCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const displayProcessedCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.processedCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const displaySuccessCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.successCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const displayFailureCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.failureCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const currentStatus = computed(() => {
    if (!progressInfo.value) return 'PENDING';

    const eventType = progressInfo.value.eventType;
    switch (eventType) {
      case 'COMPLETE':
      case 'COMPLETED':
        return 'COMPLETED';
      case 'FAILED':
      case 'ERROR':
        return 'FAILED';
      case 'PROGRESS':
      case 'PROCESSING':
      default:
        return 'PROCESSING';
    }
  });

  const canClose = computed(() => {
    const status = currentStatus.value;
    return status === 'COMPLETED' || status === 'FAILED';
  });

  // 监听taskId变化
  watch(
    () => props.taskId,
    (newTaskId) => {
      if (newTaskId) {
        initProgress();
      }
    },
    { immediate: true }
  );

  // 组件卸载时清理
  onUnmounted(() => {
    if (pollInterval) {
      clearInterval(pollInterval);
    }
  });

  // 初始化进度
  function initProgress() {
    // 初始化默认数据
    progressInfo.value = {
      taskId: props.taskId,
      eventType: 'PROGRESS',
      progress: 0,
      totalCount: 0,
      processedCount: 0,
      successCount: 0,
      failureCount: 0,
      message: '正在初始化...',
      timestamp: Date.now(),
    };

    // 启动轮询
    startPolling();
  }

  // 数据验证和解析工具函数
  function validateAndParseProgressData(data: any): ProgressInfo | null {
    if (!data || typeof data !== 'object') {
      console.warn('无效的进度数据格式:', data);
      return null;
    }

    // 验证必要字段
    const taskId = data.taskId || props.taskId;
    if (!taskId) {
      console.warn('缺少任务ID:', data);
      return null;
    }

    // 解析和验证数值字段
    const parseNumber = (value: any, defaultValue: number = 0): number => {
      // 处理 null、undefined、空字符串
      if (value === null || value === undefined || value === '') {
        return defaultValue;
      }

      // 如果已经是数字类型
      if (typeof value === 'number') {
        return isNaN(value) ? defaultValue : Math.max(0, value);
      }

      // 如果是字符串，尝试转换
      if (typeof value === 'string') {
        const trimmed = value.trim();
        if (trimmed === '') return defaultValue;
        const parsed = Number(trimmed);
        return isNaN(parsed) ? defaultValue : Math.max(0, parsed);
      }

      // 其他类型尝试直接转换
      const parsed = Number(value);
      return isNaN(parsed) ? defaultValue : Math.max(0, parsed);
    };

    // 解析进度百分比，确保在0-100范围内
    const progress = Math.min(100, Math.max(0, parseNumber(data.progress, 0)));

    // 解析计数字段
    const totalCount = parseNumber(data.totalCount, 0);
    const processedCount = parseNumber(data.processedCount, 0);
    const successCount = parseNumber(data.successCount, 0);
    const failureCount = parseNumber(data.failureCount, 0);

    // 数据一致性检查和修正
    let correctedProcessedCount = processedCount;
    let correctedSuccessCount = successCount;
    let correctedFailureCount = failureCount;

    // 检查已处理数量是否超过总数量
    if (processedCount > totalCount && totalCount > 0) {
      console.warn('数据不一致：已处理数量超过总数量，进行修正', {
        原始已处理: processedCount,
        总数量: totalCount,
      });
      correctedProcessedCount = totalCount;
    }

    // 检查成功+失败数量是否超过已处理数量
    if (successCount + failureCount > correctedProcessedCount && correctedProcessedCount > 0) {
      console.warn('数据不一致：成功+失败数量超过已处理数量，进行修正', {
        原始成功: successCount,
        原始失败: failureCount,
        已处理: correctedProcessedCount,
      });

      // 按比例缩放成功和失败数量
      const total = successCount + failureCount;
      if (total > 0) {
        const ratio = correctedProcessedCount / total;
        correctedSuccessCount = Math.floor(successCount * ratio);
        correctedFailureCount = correctedProcessedCount - correctedSuccessCount;
      }
    }

    // 如果进度为100%但处理数量不等于总数量，进行修正
    if (progress >= 100 && totalCount > 0 && correctedProcessedCount < totalCount) {
      console.log('进度100%时修正处理数量', {
        原始已处理: correctedProcessedCount,
        修正为: totalCount,
      });
      correctedProcessedCount = totalCount;

      // 如果没有明确的成功失败数量，假设都成功
      if (correctedSuccessCount === 0 && correctedFailureCount === 0) {
        correctedSuccessCount = totalCount;
      }
    }

    // 解析事件类型并标准化
    const eventType = normalizeEventType(data.eventType);

    // 解析时间戳
    const timestamp = data.timestamp ? Number(data.timestamp) : Date.now();
    if (isNaN(timestamp)) {
      console.warn('无效的时间戳:', data.timestamp);
    }

    // 构建最终的进度信息对象
    const result = {
      taskId,
      eventType,
      message: data.message || getDefaultMessage(eventType, progress),
      errorMessage: data.errorMessage || undefined,
      timestamp,
      progress,
      totalCount,
      processedCount: correctedProcessedCount,
      successCount: correctedSuccessCount,
      failureCount: correctedFailureCount,
    };

    console.log('数据解析完成:', {
      原始数据: {
        progress: data.progress,
        totalCount: data.totalCount,
        processedCount: data.processedCount,
        successCount: data.successCount,
        failureCount: data.failureCount,
      },
      解析结果: result,
    });

    return result;
  }

  // 标准化事件类型
  function normalizeEventType(eventType: any): string {
    if (!eventType || typeof eventType !== 'string') {
      return 'PROGRESS';
    }

    const type = eventType.toUpperCase().trim();

    // 事件类型映射表
    const eventTypeMap: Record<string, string> = {
      COMPLETE: 'COMPLETE',
      COMPLETED: 'COMPLETE',
      FINISH: 'COMPLETE',
      FINISHED: 'COMPLETE',
      SUCCESS: 'COMPLETE',
      DONE: 'COMPLETE',

      FAILED: 'FAILED',
      FAIL: 'FAILED',
      ERROR: 'FAILED',
      EXCEPTION: 'FAILED',

      PROGRESS: 'PROGRESS',
      PROCESSING: 'PROGRESS',
      RUNNING: 'PROGRESS',
      IN_PROGRESS: 'PROGRESS',

      PAUSED: 'PAUSED',
      PAUSE: 'PAUSED',
      SUSPENDED: 'PAUSED',

      CANCELLED: 'CANCELLED',
      CANCELED: 'CANCELLED',
      CANCEL: 'CANCELLED',
      STOPPED: 'CANCELLED',

      PENDING: 'PENDING',
      WAITING: 'PENDING',
      QUEUED: 'PENDING',
    };

    return eventTypeMap[type] || type;
  }

  // 根据事件类型和进度获取默认消息
  function getDefaultMessage(eventType: string, progress: number): string {
    switch (eventType) {
      case 'COMPLETE':
        return '导入完成';
      case 'FAILED':
        return '导入失败';
      case 'PAUSED':
        return '导入已暂停';
      case 'CANCELLED':
        return '导入已取消';
      case 'PENDING':
        return '等待开始导入';
      case 'PROGRESS':
      default:
        return `正在导入中... ${progress}%`;
    }
  }

  // 获取进度信息
  async function fetchProgress(retryCount = 0) {
    try {
      const response = await importProgressApi.getProgress(props.taskId);
      console.log('获取进度响应:', response);

      // 加强数据解析和验证
      const parsedData = validateAndParseProgressData(response);

      if (!parsedData) {
        console.error('数据解析失败，使用默认数据');
        // 使用默认数据，避免界面异常
        progressInfo.value = {
          taskId: props.taskId,
          eventType: 'PROGRESS',
          message: '正在处理中...',
          timestamp: Date.now(),
          progress: 0,
          totalCount: 0,
          processedCount: 0,
          successCount: 0,
          failureCount: 0,
        };
        return;
      }

      // 更新进度信息
      progressInfo.value = parsedData;

      console.log('处理后的进度信息:', progressInfo.value);

      // 根据事件类型执行相应的处理逻辑
      handleEventTypeSpecificLogic(parsedData);
    } catch (error) {
      console.error('获取进度失败:', error);

      // 如果是任务刚创建，进度信息可能还没初始化，进行重试
      if (retryCount < 3) {
        console.log(`进度查询失败，${2}秒后重试 (${retryCount + 1}/3)`);
        setTimeout(() => {
          fetchProgress(retryCount + 1);
        }, 2000);
      } else {
        // 重试次数用完，设置错误状态
        progressInfo.value = {
          taskId: props.taskId,
          eventType: 'FAILED',
          message: '获取进度信息失败',
          errorMessage: error instanceof Error ? error.message : '未知错误',
          timestamp: Date.now(),
          progress: 0,
          totalCount: 0,
          processedCount: 0,
          successCount: 0,
          failureCount: 0,
        };
      }
    }
  }

  // 根据事件类型执行特定逻辑
  function handleEventTypeSpecificLogic(data: ProgressInfo) {
    const { eventType, progress, successCount, failureCount, totalCount } = data;

    switch (eventType) {
      case 'COMPLETE':
        console.log('任务完成，停止轮询');
        // 清理轮询定时器
        if ((window as any).pollInterval) {
          clearInterval((window as any).pollInterval);
        }
        // 显示完成消息
        if (successCount > 0 || failureCount > 0) {
          message.success(`导入完成！成功 ${successCount} 条，失败 ${failureCount} 条`);
        } else {
          message.success('导入完成！');
        }
        break;

      case 'FAILED':
        console.log('任务失败，停止轮询');
        // 清理轮询定时器
        if ((window as any).pollInterval) {
          clearInterval((window as any).pollInterval);
        }
        // 显示错误消息
        message.error(data.errorMessage || '导入失败');
        break;

      case 'CANCELLED':
        console.log('任务已取消，停止轮询');
        // 清理轮询定时器
        if ((window as any).pollInterval) {
          clearInterval((window as any).pollInterval);
        }
        break;

      case 'PROGRESS':
        // 进度更新，记录日志
        console.log(`进度更新: ${progress}% (${data.processedCount}/${totalCount})`);
        break;

      case 'PAUSED':
        console.log('任务已暂停');
        break;

      default:
        console.log('未知事件类型:', eventType);
    }
  }

  function getProgressStatus(): 'success' | 'exception' | 'active' | 'normal' {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return 'success';
    if (status === 'FAILED') return 'exception';
    if (status === 'PROCESSING') return 'active';
    return 'normal';
  }

  function getProgressColor(): string {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return '#52c41a';
    if (status === 'FAILED') return '#ff4d4f';
    if (status === 'PAUSED') return '#722ed1';
    return '#1890ff';
  }

  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatTime(timestamp: number): string {
    if (!timestamp) return '-';
    return new Date(timestamp).toLocaleString();
  }

  function getLogClass(level: string): string {
    return `log-${level}`;
  }

  // 轮询获取进度
  function startPolling() {
    console.log('开始轮询进度，taskId:', props.taskId);

    let pollCount = 0;
    const maxPolls = 120; // 最多轮询6分钟

    pollInterval = setInterval(async () => {
      try {
        pollCount++;
        console.log(`轮询第${pollCount}次`);

        const result = await getImportProgress(props.taskId);

        console.log('获取到进度数据:', result);

        const parsedData = parseProgressData(result);
        progressInfo.value = parsedData;

        // 检查是否完成
        if (parsedData.eventType === 'COMPLETE' || parsedData.progress >= 100) {
          console.log('任务完成，停止轮询');
          if (pollInterval) {
            clearInterval(pollInterval);
            pollInterval = null;
          }

          if (parsedData.successCount > 0 || parsedData.failureCount > 0) {
            message.success(`导入完成！成功 ${parsedData.successCount} 条，失败 ${parsedData.failureCount} 条`);
          } else {
            message.success('导入完成！');
          }
          return;
        }

        // 检查是否失败
        if (parsedData.eventType === 'FAILED') {
          console.log('任务失败，停止轮询');
          if (pollInterval) {
            clearInterval(pollInterval);
            pollInterval = null;
          }
          message.error(parsedData.errorMessage || '导入失败');
          return;
        }

        // 超时处理
        if (pollCount >= maxPolls) {
          console.log('轮询超时');
          if (pollInterval) {
            clearInterval(pollInterval);
            pollInterval = null;
          }
          message.warning('获取进度超时，请手动刷新查看结果');
        }
      } catch (error) {
        console.error('轮询失败:', error);
      }
    }, 2000); // 每2秒查询一次
  }

  function getAlertType(): 'success' | 'info' | 'warning' | 'error' {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return 'success';
    if (status === 'FAILED') return 'error';
    return 'info';
  }

  function handleClose() {
    visible.value = false;
    emit('close');
  }
</script>

<style lang="less" scoped>
  .import-progress-container {
    padding: 10px;
    .task-info {
      margin-bottom: 24px;
    }

    .progress-section {
      margin-bottom: 24px;

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h4 {
          margin: 0;
        }

        .progress-text {
          font-size: 18px;
          font-weight: bold;
          color: #1890ff;
        }
      }

      .progress-details {
        margin-top: 16px;
      }
    }

    .status-message,
    .error-message {
      margin-bottom: 16px;
    }

    .action-buttons {
      margin-bottom: 24px;
      text-align: center;
    }

    .log-section {
      .log-container {
        max-height: 200px;
        overflow-y: auto;
        background: #f5f5f5;
        padding: 12px;
        border-radius: 4px;

        .log-item {
          margin-bottom: 4px;
          font-size: 12px;

          .log-time {
            color: #666;
            margin-right: 8px;
          }

          &.log-info {
            color: #333;
          }

          &.log-warn {
            color: #fa8c16;
          }

          &.log-error {
            color: #ff4d4f;
          }
        }
      }
    }
  }
</style>
